<div class="v2-finances-content">
    <div class="v2-finances-header">
        <h2 class="v2-finances-title">Donate - United States</h2>
    </div>

    <div class="v2-finances-grid">
        <!-- Left Column - Form -->
        <div class="v2-finances-form-section">
            <form id="us-donation-form" class="v2-finances-form" method="POST" action="{{ route('v2.account.finances.donate') }}">
                @csrf

                @if($errors->any())
                    <div class="v2-alert v2-alert-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <div class="v2-form-group">
                    <label for="donation-type" class="v2-form-label">Is this a one-time or recurring donation?</label>
                    <select id="donation-type" name="donation_type" class="v2-form-select" required>
                        <option value="">Select one...</option>
                        <option value="one-time" {{ old('donation_type') == 'one-time' ? 'selected' : '' }}>One-time</option>
                        <option value="recurring" {{ old('donation_type') == 'recurring' ? 'selected' : '' }}>Recurring</option>
                    </select>
                </div>

                <div class="v2-form-group" id="frequency-group" style="display: none;">
                    <label for="donation-frequency" class="v2-form-label">Donation frequency</label>
                    <select id="donation-frequency" name="donation_frequency" class="v2-form-select">
                        <option value="">Select one...</option>
                        <option value="weekly" {{ old('donation_frequency') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                        <option value="monthly" {{ old('donation_frequency') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                    </select>
                </div>

                <div class="v2-form-group">
                    <label class="v2-form-label">Recipient</label>

                    <div class="v2-recipient-section">
                        <div class="v2-recipient-item">
                            <label for="amount-commonchange" class="v2-recipient-label">Common Change Operations</label>
                            <div class="v2-amount-input">
                                <span class="v2-currency-symbol">$</span>
                                <input type="number" id="amount-commonchange" name="amount_commonchange"
                                       class="v2-form-input" placeholder="0.00" step="0.01" min="0"
                                       value="{{ old('amount_commonchange') }}">
                            </div>
                        </div>

                        <div class="v2-recipient-item">
                            <label for="amount-group" class="v2-recipient-label">My Group</label>
                            <div class="v2-amount-input">
                                <span class="v2-currency-symbol">$</span>
                                <input type="number" id="amount-group" name="amount_group"
                                       class="v2-form-input" placeholder="0.00" step="0.01" min="0"
                                       value="{{ old('amount_group') }}">
                            </div>
                        </div>

                        <div class="v2-total-section">
                            <strong>Total: $<span id="total-amount">0.00</span></strong>
                        </div>
                    </div>
                </div>

                <div class="v2-form-actions">
                    <button type="button" id="submit-donate" class="v2-btn v2-btn-primary">Donate</button>
                </div>
            </form>
        </div>

        <!-- Right Column - Information -->
        <div class="v2-finances-info-section">
            <div class="v2-info-card">
                <h3 class="v2-info-title">How can I change recurring donations?</h3>
                <p class="v2-info-text">
                    If you would like to make a change to your contribution, please contact us at
                    <a href="mailto:<EMAIL>" class="v2-link"><EMAIL></a>
                </p>

                <div class="v2-info-highlight">
                    <strong>Donations are typically processed within 48-72 hours.</strong>
                </div>

                <div class="v2-powered-by">
                    <img src="{{ asset('images/stripe_logo.png') }}" alt="Powered by Stripe" class="v2-stripe-logo">
                </div>
            </div>
        </div>
    </div>
</div>

@if(!empty(env('STRIPE_PUBLIC_KEY')))
    <script src="https://checkout.stripe.com/checkout.js"></script>
@endif

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate total amount
    function calculateTotal() {
        const commonChangeAmount = parseFloat(document.getElementById('amount-commonchange').value) || 0;
        const groupAmount = parseFloat(document.getElementById('amount-group').value) || 0;
        const total = commonChangeAmount + groupAmount;
        document.getElementById('total-amount').textContent = total.toFixed(2);
    }

    // Show/hide frequency field based on donation type
    function toggleFrequencyField() {
        const donationType = document.getElementById('donation-type').value;
        const frequencyGroup = document.getElementById('frequency-group');
        const frequencySelect = document.getElementById('donation-frequency');

        if (donationType === 'recurring') {
            frequencyGroup.style.display = 'block';
            frequencySelect.required = true;
        } else {
            frequencyGroup.style.display = 'none';
            frequencySelect.required = false;
            frequencySelect.value = '';
        }
    }

    // Add event listeners
    document.getElementById('amount-commonchange').addEventListener('input', calculateTotal);
    document.getElementById('amount-group').addEventListener('input', calculateTotal);
    document.getElementById('donation-type').addEventListener('change', toggleFrequencyField);

    // Initialize
    calculateTotal();
    toggleFrequencyField();

    @if(!empty(env('STRIPE_PUBLIC_KEY')))
        // Stripe checkout configuration
        var handler = StripeCheckout.configure({
            key: '{{ env('STRIPE_PUBLIC_KEY') }}',
            image: 'https://s3.amazonaws.com/stripe-uploads/acct_1031Km2NY3YN92fOmerchant-icon-1459460783202-AppleiPadRetina.png',
            token: function(token) {
                var form = document.getElementById('us-donation-form');
                // Insert the token into the form so it gets submitted to the server
                var hiddenInput = document.createElement('input');
                hiddenInput.setAttribute('type', 'hidden');
                hiddenInput.setAttribute('name', 'stripeToken');
                hiddenInput.setAttribute('value', token.id);
                form.appendChild(hiddenInput);
                form.submit();
            }
        });

        // Form submission with validation
        document.getElementById('submit-donate').addEventListener('click', function(e) {
            e.preventDefault();

            // Clear previous errors
            const errorElements = document.querySelectorAll('.v2-field-error');
            errorElements.forEach(el => el.remove());

            let hasErrors = false;

            // Validate donation type
            const donationType = document.getElementById('donation-type').value;
            if (!donationType) {
                showFieldError('donation-type', 'Please select a donation type.');
                hasErrors = true;
            }

            // Validate frequency for recurring donations
            if (donationType === 'recurring') {
                const frequency = document.getElementById('donation-frequency').value;
                if (!frequency) {
                    showFieldError('donation-frequency', 'Please select a donation frequency.');
                    hasErrors = true;
                }
            }

            // Validate amounts
            const commonChangeAmount = parseFloat(document.getElementById('amount-commonchange').value) || 0;
            const groupAmount = parseFloat(document.getElementById('amount-group').value) || 0;

            if (commonChangeAmount <= 0 && groupAmount <= 0) {
                showFieldError('amount-commonchange', 'Please enter a donation amount.');
                hasErrors = true;
            }

            if (groupAmount > 0 && groupAmount < 3) {
                showFieldError('amount-group', 'If you\'re making a group contribution, it must be at least $3.00.');
                hasErrors = true;
            }

            if (!hasErrors) {
                const total = commonChangeAmount + groupAmount;

                // Open Stripe checkout
                handler.open({
                    name: 'CommonChange.com',
                    description: 'Common Change Donation',
                    email: '{{ Auth::user()->email }}',
                    zipCode: true,
                    panelLabel: 'Donate $' + total.toFixed(2)
                });
            }
        });

        // Close Checkout on page navigation
        window.addEventListener('popstate', function() {
            handler.close();
        });
    @else
        // Fallback if Stripe is not configured
        document.getElementById('submit-donate').addEventListener('click', function(e) {
            e.preventDefault();
            alert('Payment processing is currently unavailable. Please try again later.');
        });
    @endif

    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'v2-field-error';
        errorDiv.style.color = '#ef4444';
        errorDiv.style.fontSize = '14px';
        errorDiv.style.marginTop = '5px';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }
});
</script>
