<?php

namespace App\Models;

use App\Notifications\ResetPassword as ResetPasswordNotification;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class User extends Authenticatable implements CanResetPassword, MustVerifyEmail
{
    use HasFactory, Notifiable, SoftDeletes;

    public function discussions(): HasMany
    {
        return $this->hasMany(Discussion::class, 'creator_id');
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class, 'creator_id');
    }

    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class)
            ->withPivot('role', 'is_absent', 'email_notification')
            ->using(GroupUserPivot::class);
    }

    public function read_posts(): BelongsToMany
    {
        return $this->belongsToMany(Post::class);
    }

    public function read_issues(): BelongsToMany
    {
        return $this->belongsToMany(Issue::class);
    }

    public function read_discussions(): BelongsToMany
    {
        return $this->belongsToMany(Discussion::class);
    }

    public function default_group(): BelongsTo
    {
        return $this->belongsTo(Group::class, 'default_group_id');
    }

    public function group_invites(): HasMany
    {
        return $this->hasMany(GroupInvite::class, 'user_id');
    }

    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class);
    }

    public function mobile_apps(): HasMany
    {
        return $this->hasMany(MobileAppAccount::class, 'user_id')->where('mobile_app_accounts.logged_in', '=', 1);
    }

    public function mobile_apps_notify()
    {
        return $this->mobile_apps()->where('mobile_app_accounts.notify', '=', 1);
    }

    public function address()
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    // Define some query scopes:
    public function scopeIsActive($query)
    {
        return $query->where('suspended', 0);
    }

    public function scopeHasGroup($q, $group_id)
    {
        return $q->whereHas('groups', function (Builder $query) use ($group_id) {
            return $query->where('groups.id', $group_id);
        });
    }

    public function scopeSearchNameEmail($q, $search)
    {
        return $q->where(\DB::raw("CONCAT(firstname, ' ', lastname, ' ', email)"),
            'LIKE',
            '%'.$search.'%');
    }

    const DEFAULT_PIC = 'profile-placeholder.png';

    // Default attribute values for a new object
    protected $attributes = [
        'profile_pic' => self::DEFAULT_PIC,
        'wizard_bookmark' => 'account',
        'default_group_id' => 1,
        'can_use_mobile_app' => true,
    ];

    protected $with = ['address'];

    protected $hidden = ['password', 'remember_token', 'token'];

    protected $fillable = ['firstname', 'lastname', 'email', 'password', 'facebook', 'twitter', 'blog', 'default_email_notification', 'microsoft_id', 'facebook_id', 'apple_id'];

    public function setPasswordAttribute($cleartext)
    {
        // Only hash if not already a bcrypt hash
        if (strpos($cleartext, '$2y$') === 0) {
            $this->attributes['password'] = $cleartext;
        } else {
            $this->attributes['password'] = \Hash::make($cleartext);
        }
    }

    public function getIsAdminAttribute()
    {
        return $this->attributes['role'] == 'admin';
    }

    // for now this is the same as is_admin
    public function getCanImpersonateAttribute()
    {
        return $this->attributes['role'] == 'admin';
    }

    public function resetDefaultGroup()
    {
        $group = $this->groups()->first();
        $this->default_group_id = isset($group) ? $group->id : 0;
        $this->save();

        return $group;
    }

    // If their default group has gone away, this attempts to set it to one of their other groups
    public function getMyDefaultGroupAttribute()
    {
        $group = $this->default_group()->first();
        if (isset($group) && $group->isMember(Auth::User())) {
            return $group;
        }

        return $this->resetDefaultGroup();
    }

    public static function set_current_group($group)
    {
        session()->put('current_group', $group);
    }

    public static function get_current_group()
    {
        $group = session()->get('current_group');
        if (isset($group) && $group->isMember(Auth::User())) {
            return $group;
        }
        $group = Auth::User()->my_default_group;
        static::set_current_group($group);

        return $group;
    }

    public function roleClass()
    {
        return ($this->pivot->role == 'member') ? 'membr' : $this->pivot->role;
    }

    private static $wizard_order = [
        'account' => 'group',
        'group' => 'donation',
        'donation' => 'intro',
        'intro' => null,
    ];

    public function currentWizardRoute()
    {
        return (isset($this->wizard_bookmark)) ? 'wizard.'.$this->wizard_bookmark : 'home';
    }

    public function wizardNotComplete()
    {
        return isset($this->wizard_bookmark);
    }

    // probably could be named better.  basically, if their current wizard page is the given route
    // (that matches a section in self::$wizard_order), set bookmark to the next route in self::$wizard_order
    public function finishedWizardSection($section)
    {
        $this->wizard_bookmark = self::$wizard_order[$section];
    }

    public $errors;

    public static $rules = [
        'firstname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
        'lastname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
        'email' => 'required|email|unique:users',
        'password' => 'required|min:8|confirmed',
        'default_email_notification' => 'in:daily_digest,daily_per_discussion,per_comment,none',
        'facebook' => 'nullable|url',
        'twitter' => 'nullable|url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
        ];
    }

    public function isValid()
    {
        $rules = static::$rules;
        if (isset($this->id)) {
            $rules['email'] = $rules['email'].',email,'.$this->id;
            unset($rules['password']);
        }
        $validation = Validator::make($this->attributes, $rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }

    public function setProfilePicAttribute($value)
    {
        $this->attributes['profile_pic'] = basename($value);
    }

    public function profilePicHax($value)
    {
        $this->attributes['profile_pic'] = $value;
    }

    public function getProfilePicAttribute($value)
    {
        return '/pix/'.$value;
    }

    // Fetches the user's timezone from a cookie, if it's set:
    public static function get_local_tz()
    {
        static $user_tz;  // make this static so we can cache it

        if (isset($user_tz)) {
            return $user_tz;
        } // only set this value once

        $user_tz = (isset($_COOKIE['user_tz_name'])) ? $_COOKIE['user_tz_name'] : null;
        if (empty($user_tz)) {
            return null;
        }

        $user_tz = preg_replace('/[^a-zA-Z0-9_\/]+/', '', $user_tz);
        if (empty($user_tz)) {
            return null;
        }

        return $user_tz;
    }

    // Users who have the default thumbnail will filter to the bottom of the list.
    public static function picSort($a, $b)
    {
        if ($a->attributes['profile_pic'] == self::DEFAULT_PIC && $b->attributes['profile_pic'] == self::DEFAULT_PIC) {
            return 0;
        } elseif ($a->attributes['profile_pic'] == self::DEFAULT_PIC) {
            return 1;
        } elseif ($b->attributes['profile_pic'] == self::DEFAULT_PIC) {
            return -1;
        } else {
            return 0;
        }
    }

    public function sendPasswordResetNotification($token)
    {
        ResetPasswordNotification::createUrlUsing(function () use ($token) {
            return route('password.getreset', ['token' => $token]);
        });
        $this->notify(new ResetPasswordNotification($token));
    }
}
